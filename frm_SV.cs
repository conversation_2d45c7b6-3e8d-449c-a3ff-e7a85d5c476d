﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OnTapCuoiKy1
{
    public partial class frm_SV : Form
    {
        public void ResetForm()
        {
            txt_DiaChi.Text = "";
            txt_HoTen.Text = "";
            txt_MSSV.Text = "";
            dt_NgaySinh.ResetText();
            cb_Lop.ResetText(); 
        }
        public void LoadForm()
        {
            string sql = $"Select * From SinhVien";
            dtgv_SinhVien.DataSource = lopchung.LayDuLieuDB(sql);
            string sql1 = "SELECT MaLop, TenLop FROM Lop";
            DataTable dtLop = lopchung.LayDuLieuDB(sql1);
            cb_Lop.DataSource = dtLop;
            cb_Lop.DisplayMember = "TenLop";
            cb_Lop.ValueMember = "MaLop";
        }
        public frm_SV()
        {
            InitializeComponent();
        }

        LopDungChung lopchung = new LopDungChung(); 
        private void btn_Them_Click(object sender, EventArgs e)
        {
            string sql = $"Insert into SinhVien (MSSV, HoTen, NgaySinh, DiaChi, MaLop) Values('{txt_MSSV.Text}','{txt_HoTen.Text}', '{dt_NgaySinh.Value}', '{txt_DiaChi.Text}', '{cb_Lop.SelectedValue}')";
            int kq = lopchung.ThemSuaXoa(sql); 
            if (kq >= 1)
            {
                MessageBox.Show("Them Thanh Cong"); 
            }else
            {
                MessageBox.Show("Them That Bai"); 
            }
            LoadForm(); 
        }

        private void btn_Sua_Click(object sender, EventArgs e)
        {
            string sql = $"Update SinhVien Set HoTen ='{txt_HoTen.Text}'" +
                $"NgaySinh = '{dt_NgaySinh.Value}'" +
                $"DiaChi ='{txt_DiaChi.Text}'" +
                $"MaLop ='{cb_Lop.SelectedValue}' Where MSSV = '{txt_MSSV.Text}'";
            int kq = lopchung.ThemSuaXoa(sql);
            if (kq >= 1)
            {
                MessageBox.Show("Sua Thanh Cong");
            }
            else
            {
                MessageBox.Show("Sua That Bai");
            }
            LoadForm(); 
        }

        private void btn_Xoa_Click(object sender, EventArgs e)
        {
            string sql = $"Delete From SinhVien Where MSSV ='{txt_MSSV.Text}' ";
            int kq = lopchung.ThemSuaXoa(sql);
            if (kq >= 1)
            {
                MessageBox.Show("Sua Thanh Cong");
            }
            else
            {
                MessageBox.Show("Sua That Bai");
            }
            LoadForm(); 
        }

        private void dtgv_SinhVien_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {

        }

        private void dtgv_SinhVien_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            txt_MSSV.Text = dtgv_SinhVien.CurrentRow.Cells["MSSV"].Value.ToString();
            txt_HoTen.Text = dtgv_SinhVien.CurrentRow.Cells["HoTen"].Value.ToString();
            txt_DiaChi.Text = dtgv_SinhVien.CurrentRow.Cells["DiaChi"].Value.ToString();
            dt_NgaySinh.Text = dtgv_SinhVien.CurrentRow.Cells["NgaySinh"].Value.ToString();
            cb_Lop.Text = dtgv_SinhVien.CurrentRow.Cells["MaLop"].Value.ToString();
        }

        private void frm_SV_Load(object sender, EventArgs e)
        {
            LoadForm();
        }
    }
}
