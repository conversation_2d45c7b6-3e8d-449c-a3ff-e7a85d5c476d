﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data;
using System.Data.SqlClient;
namespace OnTapCuoiKy1
{
    class LopDungChung
    {
        SqlConnection conn;
        public LopDungChung()
        {
            string chuoikn = @"Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=D:\C#\OnTap\OnTapCuoiKy1\OnTapCuoiKy1\QLSV.mdf;Integrated Security=True";
            conn = new SqlConnection(chuoikn);
        }

        public int ThemSuaXoa(string sql)
        {
            SqlCommand comm = new SqlCommand(sql, conn);
            conn.Open();
            int kq = comm.ExecuteNonQuery();
            conn.Close();
            return kq;
        }

        public object LayGiaTriDB (string sql)
        {
            SqlCommand comm = new SqlCommand(sql, conn);
            conn.Open(); 
            object kq = comm.ExecuteScalar();
            conn.Close();
            return kq; 
        }

        public DataTable LayDuLieuDB (string sql)
        {
            SqlDataAdapter adapter = new SqlDataAdapter(sql, conn);
            DataTable dt = new DataTable();
            adapter.Fill(dt);
            return dt; 
        }
    }
}
