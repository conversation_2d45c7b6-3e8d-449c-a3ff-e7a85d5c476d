﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OnTapCuoiKy1
{
    public partial class frm_Main : Form
    {
        public frm_Main()
        {
            InitializeComponent();
        }

        private void frm_Main_Load(object sender, EventArgs e)
        {

        }

        private void quanLySVToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (Application.OpenForms["frm_SV"] == null)
            {
                frm_SV sv = new frm_SV();
                sv.MdiParent = this;
                sv.Show();
            } else Application.OpenForms["frm_SV"].Activate(); 

        }
    }
}
